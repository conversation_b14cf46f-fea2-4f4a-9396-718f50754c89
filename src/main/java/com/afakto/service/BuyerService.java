package com.afakto.service;

import com.afakto.domain.Buyer;
import com.afakto.repository.BuyerRepository;
import com.afakto.repository.ContractRepository;
import com.afakto.repository.CreditLimitRequestRepository;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.ContractDTO;
import com.afakto.service.dto.SearchRequest;
import com.afakto.service.feign.InsurerSearchResult;
import com.afakto.service.insurer.InsurerAtradiusService;
import com.afakto.service.insurer.InsurerCofaceService;
import com.afakto.service.mapper.BuyerMapper;
import com.afakto.service.mapper.CompanyMapper;
import com.afakto.service.mapper.ContractMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

/**
 * Service for managing {@link Buyer}.
 */
@RequiredArgsConstructor
@Service
@Slf4j
@Transactional
public class BuyerService {
    private final BuyerMapper buyerMapper;
    private final BuyerRepository buyerRepository;
    private final ContractMapper contractMapper;
    private final ContractRepository contractRepository;
    private final CreditLimitRequestRepository creditLimitRequestRepository;
    private final InsurerAtradiusService insurerAtradiusService;
    private final InsurerCofaceService insurerCofaceService;
    private final CompanyMapper companyMapper;

    public BuyerDTO save(BuyerDTO buyerDTO) {
        log.debug("Request to save Buyer : {}", buyerDTO);
        Buyer buyer = buyerMapper.toEntity(buyerDTO);
        buyer = buyerRepository.save(buyer);
        return buyerMapper.toDto(buyer);
    }

    public Optional<BuyerDTO> partialUpdate(BuyerDTO buyerDTO) {
        log.debug("Request to partially update Buyer : {}", buyerDTO);

        return buyerRepository
            .findById(buyerDTO.getId())
            .map(existingBuyer -> {
                buyerMapper.partialUpdate(existingBuyer, buyerDTO);
                return existingBuyer;
            })
            .map(buyerMapper::toDto);
    }

    @Transactional(readOnly = true)
    public Optional<BuyerDTO> findOne(UUID id) {
        log.debug("Request to get Buyer : {}", id);
        return buyerRepository.findById(id).map(buyerMapper::toDto);
    }

    public void delete(UUID id) {
        log.debug("Request to delete Buyer : {}", id);
        creditLimitRequestRepository.deleteByBuyerId(id);
        buyerRepository.deleteById(id);
    }

    public BuyerDTO updateCreditLimit(UUID id) {
        var entity = buyerRepository.findById(id).orElseThrow();
        var insurerName = contractRepository.getInsurerName(entity.getCompany());

        return switch (insurerName.toLowerCase()) {
            case "atradius" -> buyerMapper.toDto(insurerAtradiusService.updateCreditLimit(entity));
            case "coface" -> buyerMapper.toDto(insurerCofaceService.updateCreditLimit(entity));
            default -> throw new IllegalArgumentException("Unknown insurer: " + insurerName);
        };
    }

    public Optional<ContractDTO> getContract(BuyerDTO buyerDTO) {

        var contracts = contractRepository.findAllByCompanyAndFinancialInformationCurrencyAndActivationDateIsBefore(
            companyMapper.toEntity(buyerDTO.getCompany()),
            buyerDTO.getCurrency(),
            LocalDate.now());

        return contracts.stream()
            .findFirst()
            .map(contractMapper::toDto);
    }

    public InsurerSearchResult searchBuyer(SearchRequest searchRequest) {
        return insurerCofaceService.searchBuyer(searchRequest);
    }
}
