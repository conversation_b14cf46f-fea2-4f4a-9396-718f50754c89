package com.afakto.service.insurer.coface;

import com.afakto.domain.enumeration.NumberType;
import com.afakto.service.dto.AddressDTO;
import com.afakto.service.dto.BuyerDTO;
import com.afakto.service.dto.SearchRequest;
import com.afakto.service.feign.FeignInsurerCofaceHelper;
import com.afakto.service.feign.FeignInsurerCofaceService;
import com.afakto.service.insurer.coface.mapping.CofaceNumberTypeMapping;
import com.afakto.service.insurer.coface.mapping.ConsultCofaceModel;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.HashMap;

import static com.afakto.batch.Utils.iso3ToIso2;
import static com.afakto.batch.Utils.mapTo3LetterCode;

@RequiredArgsConstructor
@Service
@Slf4j
public class Search {

    private final FeignInsurerCofaceService feignInsurerService;
    private final FeignInsurerCofaceHelper feignInsurerCofaceHelper;
    private final CofaceNumberTypeMapping cofaceNumberTypeMapping;
    private final ObjectMapper objectMapper;

    public ConsultCofaceModel search(SearchRequest searchRequest) throws FeignException {
        String countryCode = mapTo3LetterCode(searchRequest.getCountry());

        JsonNode result;
        try (var ignored = new CofaceRequestContextScope(feignInsurerCofaceHelper.fromTest())) {
            if (searchRequest.getInsurerCode() != null)
                result = feignInsurerService.getProductsForEasyNumber(searchRequest.getInsurerCode());
            else if (searchRequest.getNumberType() != null && cofaceNumberTypeMapping.getIntoCofaceNumberType().getOrDefault(countryCode, new HashMap<>()).containsKey(searchRequest.getNumberType())) {
                String cofaceIdentifier = cofaceNumberTypeMapping.getIntoCofaceNumberType().get(countryCode).get(searchRequest.getNumberType());
                if (countryCode.equalsIgnoreCase("ESP"))
                    if (!searchRequest.getNumber().startsWith("ES"))
                        searchRequest.setNumber("ES" + searchRequest.getNumber());
                result = feignInsurerService.getBuyerFromIdentifier(countryCode, cofaceIdentifier, searchRequest.getNumber());
            } else
                result = feignInsurerService.getBuyerFromCompanyName(countryCode, searchRequest.getName());
        }

        try {
            return objectMapper.treeToValue(result, ConsultCofaceModel.class);
        } catch (JsonProcessingException e) {
            log.error(e.getMessage());
            return null;
        }
    }

    public BuyerDTO mapToBuyerData(ConsultCofaceModel.Company cofaceCompany) {
        BuyerDTO buyer = new BuyerDTO();
        AddressDTO addressDTO = getAddress(cofaceCompany.companyDetails().address());
        updateIdentifier(cofaceCompany, buyer);
        return buyer.setAddress(addressDTO).setName(cofaceCompany.companyDetails().name());
    }

    private void updateIdentifier(ConsultCofaceModel.Company cofaceCompany, BuyerDTO buyer) {
        if (cofaceCompany.mainIdentifier() != null) {
            if (setBuyerIdentifier(buyer, cofaceCompany.mainIdentifier()))
                return;
        }

        if (cofaceCompany.secondaryIdentifiers() == null)
            return;
        for (ConsultCofaceModel.Identifier secondaryIdentifier : cofaceCompany.secondaryIdentifiers()) {
            if (setBuyerIdentifier(buyer, secondaryIdentifier))
                return;
        }
    }

    private boolean setBuyerIdentifier(BuyerDTO buyer, ConsultCofaceModel.Identifier cofaceIdentifier) {
        NumberType numberType = cofaceNumberTypeMapping.getFromCofaceNumberType().get(cofaceIdentifier.type());
        if (numberType == null)
            return false;
        buyer.setNumber(cofaceIdentifier.value()).setNumberType(numberType);
        return true;
    }

    private AddressDTO getAddress(ConsultCofaceModel.CompanyDetails.Address address) {
        return new AddressDTO()
            .setCity(address.city())
            .setCountry(iso3ToIso2(address.countryCode()).toLowerCase())
            .setPostalCode(address.postalCode())
            .setStreetName(address.streets() != null && !address.streets().isEmpty() ? address.streets().getFirst() : null);
    }

    public String getCofaceErrorCode(String message) {
        int startIndex = message.indexOf("[{");
        int endIndex = message.lastIndexOf("}]") + "}]".length();

        if (startIndex == -1 || endIndex <= startIndex)
            throw new RuntimeException(message);

        message = message.substring(startIndex, endIndex);
        try {
            JsonNode node = objectMapper.readTree(message).get(0);
            String code = node.get("code").asText();

            if (code.equals("Client403-SOR_BR230236"))
                return "COUNTRY_NOT_ALLOWED";
            if (code.equals("Client412"))
                return "VAT_INVALID";
            return message;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Couldn't parse error message: " + message);
        }
    }

}
