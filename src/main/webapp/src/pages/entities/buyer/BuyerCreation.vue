<template>
  <q-dialog persistent @update:model-value="val => $emit('update:modelValue', val)">
    <q-card class="column no-wrap q-pa-md" style="min-width: 340px; max-width: 600px;">

      <!-- Toolbar -->
      <q-toolbar class="bg-backgroundSecondary q-pa-sm q-mb-md">
        <div class="text-h6">{{ $t('afaktoApp.buyer.home.titleCreate') }}</div>
        <q-space />
        <q-btn dense flat icon="close" round @click="closeDialog">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <q-form ref="formRef" class="q-gutter-md">

        <!-- Step 1: Identifier input -->
        <transition name="fade">
          <div v-if="!identifierConfirmed">
            <q-input
              v-model="identifier"
              :label="$t('afaktoApp.buyer.identifier')"
              :rules="[val => !!val || 'Required']"
              clearable
              dense
              filled
              @update:model-value="checkIdentifierInput"
            />
            <div class="row justify-end">
              <q-btn
                :disable="!identifier"
                color="primary"
                label="Next"
                @click="confirmIdentifier"
              />
            </div>
          </div>
        </transition>

        <!-- Step 2: Country select -->
        <transition name="fade">
          <div v-if="identifierConfirmed && !countryConfirmed">
            <q-badge class="q-mb-sm" color="blue">
              {{ identifierType?.label || 'Search by name' }}
            </q-badge>

            <q-select
              v-model="entity.address.country"
              :label="$t('afaktoApp.contract.country')"
              :options="filteredCountries"
              :rules="[required]"
              clearable
              dense
              emit-value
              filled
              map-options
              option-label="name"
              option-value="code"
              @update:model-value="checkCountry"
            >
              <template v-if="entity.address.country" #prepend>
                <span :class="`fi fi-${entity.address.country}`" />
              </template>
              <template #option="scope">
                <q-item dense v-bind="scope.itemProps">
                  <q-item-section side>
                    <span :class="`fi fi-${scope.opt.code}`" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label>{{ scope.opt.name }}</q-item-label>
                  </q-item-section>
                </q-item>
              </template>
            </q-select>

            <div class="row justify-end">
              <q-btn
                :disable="!entity.address.country"
                color="primary"
                label="Next"
                @click="confirmCountry"
              />
            </div>
          </div>
        </transition>

        <!-- Step 3: Entity search results -->
        <transition name="fade">
          <div v-if="searchLegalEntity !== null">
            <div class="row justify-end q-mb-sm">
              <q-btn color="secondary" label="Manual" @click="goToNextStep" />
            </div>

            <div v-if="searchLegalEntity === false">
              <buyer-enrich-selector
                :model-value="legalEntities"
                @selected-entity="copyEntity"
              />
            </div>

            <div v-else-if="searchLegalEntity === true">
              <q-spinner-dots color="primary" size="md" />
              <div class="text-caption q-mt-sm">Loading...</div>
            </div>
          </div>
        </transition>

      </q-form>
    </q-card>
  </q-dialog>
</template>


<script setup>
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';

import BuyerService from 'pages/entities/buyer/buyer.service';
import { required } from 'src/components/rules';
import { findIdType } from 'src/util/findIdType.js';
import useNotifications from 'src/util/useNotifications';
import BuyerEnrichSelector from 'pages/entities/buyer/BuyerEnrichSelector.vue';

const { notifyError } = useNotifications();
const { t, tm } = useI18n();
const emit = defineEmits(['update:modelValue', 'openNewBuyerDetails']);

const props = defineProps({
  entity: Object
});

const formRef = ref(null);

const { identifier, identifierType, countryFilter, filteredCountries, evaluateIdentifier, checkCountry } = findIdType(
  props.entity,
  useI18n()
);

const checkIdentifierInput = async inputValue => {
  await evaluateIdentifier(inputValue);
  if (identifierType.value.country) props.entity.address.country = identifierType.value.country;
  else if (Object.keys(countryFilter.value).length > 1) props.entity.address.country = null;
};

const identifierConfirmed = ref(false);
const countryConfirmed = ref(false);
const searchLegalEntity = ref(null);
const legalEntities = ref([]);

function confirmIdentifier() {
  identifierConfirmed.value = true;
}

async function confirmCountry() {
  countryConfirmed.value = true;
  await getLegalEntity();
}

async function getLegalEntity() {
  if (!identifier.value) return;

  if (identifierType.value?.type) {
    props.entity.number = identifier.value;
    props.entity.numberType = identifierType.value.type;
  } else {
    props.entity.name = identifier;
    props.entity.number = null;
    props.entity.numberType = null;
  }

  searchLegalEntity.value = true;
  legalEntities.value = [];

  try {
    const enrichedBuyers = await BuyerService.search(props.entity);
    legalEntities.value = enrichedBuyers.data.buyers;
  } catch (err) {
    if (err.response?.status === 400 && err.response.data?.errorCode)
      notifyError(t(`afaktoApp.buyer.enrichmentErrors.${err.response.data.errorCode}`));
    else notifyError(err);
  }
  searchLegalEntity.value = false;
}

const copyEntity = row => {
  props.entity.address = row.address;
  props.entity.number = row.number;
  props.entity.numberType = row.numberType;
  props.entity.name = row.name;
  goToNextStep();
};

const closeDialog = () => emit('update:modelValue', false);

async function goToNextStep() {
  emit('openNewBuyerDetails', props.entity);
  closeDialog();
}

const numberTypeOptions = computed(() => {
  const countryCode = props.entity?.address?.country.toUpperCase();
  if (countryCode === null) return [];
  const countryNumberTypes = tm(`afaktoApp.NumberType.${countryCode}`);

  if (!countryNumberTypes || typeof countryNumberTypes !== 'object') return [];

  return Object.entries(countryNumberTypes).map(([key, value]) => ({
    label: value.label,
    value: key
  }));
});

</script>
