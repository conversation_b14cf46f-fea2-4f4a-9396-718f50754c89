<template>
  <q-dialog persistent @show="onDialogShow()" @update:model-value="val => $emit('update:modelValue', val)">
    <q-card class="column no-wrap" style="min-width: 1400px; height: 700px">
      <q-toolbar>
        <q-space />

        <q-btn v-if="hasRoleWriter && entity.id" :label="$t('entity.action.delete')" class="buttonNeutral" icon="delete"
               @click="onDelete" />
        <q-btn class="buttonNeutral" icon="close" to="#" @click="emit('update:modelValue', false)">
          <q-tooltip>{{ $t('entity.action.close') }}</q-tooltip>
        </q-btn>
      </q-toolbar>

      <q-form ref="buyerForm" greedy @submit="onSubmit">
        <q-tabs v-model="tab" :vertical="$q.platform.is?.desktop">
          <q-tab :label="t('afaktoApp.buyer.detail.title')" name="main" />
          <q-tab :label="t('afaktoApp.buyer.address')" name="address" />
          <q-tab :label="t('afaktoApp.buyer.contact')" name="contact" />
          <q-tab v-if="entity.id" :label="t('afaktoApp.buyer.detail.invoices')" name="invoices" />
          <q-tab v-if="entity.id" :label="t('afaktoApp.buyer.detail.insurer')" name="insurer" />
          <q-tab v-if="entity.buyerFromFactor" :label="t('afaktoApp.buyer.detail.factor')" name="factor" />
        </q-tabs>

        <q-tab-panels v-model="tab" :vertical="$q.platform.is?.desktop" animated swipeable>
          <q-tab-panel name="main">
            <q-select
              v-model="entity.company"
              :label="$t('afaktoApp.buyer.company')"
              :options="useAuthenticationStore().account.companies"
              :readonly="!hasRoleWriter || !!entity.id"
              :rules="[required]"
              class="required"
              filled
              option-label="name"
              option-value="id"
            />

            <b-input
              v-model="entity.code"
              :label="$t('afaktoApp.buyer.code')"
              :readonly="!hasRoleWriter"
              :rules="[required]"
              class="required"
            />
            <b-input
              v-model="entity.name"
              :label="$t('afaktoApp.buyer.name')"
              :loading="searchByIdentifier"
              :readonly="!hasRoleWriter"
              :rules="[required]"
              class="required"
            />

            <q-input
              v-model="entity.number"
              :fill-mask="['SIREN', 'SIRET'].includes(entity.numberType)"
              :label="$t('afaktoApp.buyer.number')"
              :loading="searchByIdentifier"
              :mask="NUMBER_TYPE_MASKS[entity.numberType]"
              :readonly="!hasRoleWriter"
              :rules="[required]"
              class="required"
              filled
              unmasked-value
            >
              <template #prepend>
                <q-select
                  v-model="entity.numberType"
                  :label="$t('afaktoApp.buyer.numberType')"
                  :option-label="numberType => $t(`afaktoApp.NumberType.${numberType}`).replaceAll('_', ' ')"
                  :options="NUMBER_TYPES"
                  :readonly="!hasRoleWriter"
                  :rules="[required]"
                  bg-color="transparent"
                  class="required"
                  style="min-width: 12em"
                />
              </template>
            </q-input>

            <b-toggle
              v-model="entity.excluded"
              :disable="!hasRoleWriter"
              :label="$t('afaktoApp.buyer.excluded') + ' - ' + $t('afaktoApp.buyer.excluded_help')"
            />
            <q-icon color="warning" name="remove_done" right size="sm" style="vertical-align: baseline" />
            <q-input
              v-if="entity.excluded"
              v-model="entity.exclusionReason"
              :label="t('afaktoApp.buyer.exclusionReason')"
              :readonly="!hasRoleWriter"
              filled
              rows="2"
              type="textarea"
            />
          </q-tab-panel>

          <q-tab-panel name="address">
            <address-comp v-model="entity.address" :readonly="!hasRoleWriter" />
          </q-tab-panel>

          <q-tab-panel name="contact">
            <contact-comp v-model="entity.contact" :readonly="!hasRoleWriter" />
          </q-tab-panel>

          <q-tab-panel name="invoices">
            <buyer-details-invoices />
          </q-tab-panel>

          <q-tab-panel name="insurer">
            <buyer-details-insurer-tab v-model="entity"></buyer-details-insurer-tab>
          </q-tab-panel>

          <q-tab-panel name="factor">
            <b-input :label="$t('afaktoApp.buyer.buyerFromFactor.factorCode')"
                     :model-value="entity.buyerFromFactor?.factorCode" readonly />
            <b-input :label="$t('afaktoApp.buyer.buyerFromFactor.number')" :model-value="entity.buyerFromFactor.number"
                     readonly />

            <b-input
              :label="$t('afaktoApp.buyer.buyerFromFactor.amountApproved')"
              :model-value="$n(entity.buyerFromFactor.amountApproved || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
              input-class="text-right"
              readonly
            >
              <template #append>
                <q-icon v-if="entity.creditLimit?.amount != entity.buyerFromFactor.amountApproved" color="warning"
                        name="warning" size="md">
                  <q-tooltip>{{ $t('afaktoApp.buyer.incoherentLimit_helper') }}</q-tooltip>
                </q-icon>
              </template>
            </b-input>

            <b-input
              :label="$t('afaktoApp.buyer.buyerFromFactor.amountOutstanding')"
              :model-value="$n(entity.buyerFromFactor?.amountOutstanding || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
              input-class="text-right"
              readonly
            >
              <template #append>
                <q-icon
                  v-if="entity.buyerFromFactor.amountOutstanding > entity.buyerFromFactor.amountApproved"
                  color="warning"
                  name="warning"
                  size="md"
                >
                  <q-tooltip>{{ $t('afaktoApp.buyer.incoherentAmount_helper') }}</q-tooltip>
                </q-icon>
              </template>
            </b-input>
            <b-input
              :label="$t('afaktoApp.buyer.buyerFromFactor.amountFunded')"
              :model-value="$n(entity.buyerFromFactor.amountFunded || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
              input-class="text-right"
              readonly
            />
            <b-input
              :label="$t('afaktoApp.buyer.buyerFromFactor.amountSecured')"
              :model-value="$n(entity.buyerFromFactor.amountSecured || 0, 'currencyCode', { currency: entity.buyerFromFactor.currency })"
              input-class="text-right"
              readonly
            />

            <entity-meta-dates :entity="entity.buyerFromFactor" />
          </q-tab-panel>
        </q-tab-panels>

        <div class="full-width text-center">
          <q-btn v-if="hasRoleWriter" :label="$t('entity.action.save')" class="buttonBrand" icon="label_important"
                 type="submit" />
        </div>
      </q-form>

      <entity-meta :entity="entity" />
    </q-card>
  </q-dialog>
</template>

<script setup>
import { useQuasar } from 'quasar';
import { nextTick, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRoute, useRouter } from 'vue-router';

import BuyerService from 'pages/entities/buyer/buyer.service';
import { required } from 'src/components/rules';
import { NUMBER_TYPE_MASKS, NUMBER_TYPES } from 'src/constants/numberType';
import EntityMeta from 'src/pages/subcomponents/EntityMeta.vue';
import EntityMetaDates from 'src/pages/subcomponents/EntityMetaDates.vue';
import { useAuthenticationStore } from 'src/stores/authentication-store';
import { setupBeforeUnload } from 'src/util/formBeforeUnload';
import useNotifications from 'src/util/useNotifications';
import AddressComp from '../../subcomponents/AddressComp.vue';
import ContactComp from '../../subcomponents/ContactComp.vue';
import BuyerDetailsInvoices from './BuyerDetailsInvoices.vue';
import BuyerDetailsInsurerTab from './BuyerDetailsInsurerTab.vue';

const hasRoleWriter = useAuthenticationStore().hasRoleWriter;
const { notifyError } = useNotifications();
const $q = useQuasar();
const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const tab = ref(route.query.tab || 'main');

const props = defineProps({
  entity: Object
});

const emit = defineEmits(['update:modelValue']);

const entity = ref({
  address: { city: '' },
  contact: { name: '' }
});

const buyerForm = ref(null);

async function onDialogShow() {
  await nextTick(() => {
    if (buyerForm.value) {
      setupBeforeUnload(t, buyerForm.value.$el, entity.value);
    } else {
      console.warn('Form not found for setupBeforeUnload');
    }
  });

  if (props.entity) {
    entity.value = props.entity;

  }

}

const searchByIdentifier = ref(false);

const onSubmit = async () => {
  BuyerService.save(entity.value)
    .then(() => router.back())
    .catch(error => notifyError(error));
};

const onDelete = () => {
  $q.dialog({
    message: t('afaktoApp.buyer.delete.question', { id: entity.value.name }),
    cancel: true
  }).onOk(() => {
    BuyerService.delete(entity.value.id)
      .then(() => {
        $q.notify({
          message: 'Deleted',
          icon: 'announcement'
        });
        router.back();
      })
      .catch(error => notifyError(error));
  });
};
</script>
