<template>
  <q-drawer overlay show-if-above side="right">
    <q-card class="backgroundSecondary" flat>
      <q-card-section class="col q-col-gutter-y-sm">
        <div class="row justify-end">
          <q-btn class="buttonNeutral" icon="close" @click="$emit('close')"></q-btn>
        </div>
        <q-item-section class="q-gutter-y-sm ">
          <div>
            <q-item-label class="show-label">
            <q-icon name="file_copy" />
            <div>Company</div>
          </q-item-label>
          <q-item-label>
            <h3>{{ modelValue?.name }}</h3>
          </q-item-label>
          </div>
        </q-item-section>
      </q-card-section>
      <q-list padding>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.company.code') }}</q-item-section>
          <q-item-section avatar>{{ modelValue?.code }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section> {{ $t('afaktoApp.company.id') }} {{ $t('afaktoApp.company.numberType') }}</q-item-section>
          <q-item-section v-if="modelValue.numberType" avatar class="show-label self-center">{{ modelValue?.numberType
            }}
          </q-item-section>
        </q-item>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.company.number') }}</q-item-section>
          <q-item-section avatar>{{ modelValue?.number }}</q-item-section>
        </q-item>
        <q-item>
          <q-item-section>{{ $t('afaktoApp.company.fiscalYearClosingMonth') }}</q-item-section>
          <q-item-section avatar>{{ getFormattedMonth(modelValue?.fiscalYearClosingMonth) }}</q-item-section>
        </q-item>
      </q-list>


      <q-separator />
      <q-list>
        <q-expansion-item dense-toggle expand-separator default-opened label="Address">
          <div class="q-pa-sm">
            <q-item>
              <q-item-section>{{ $t('afaktoApp.address.street') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.streetNumber }} {{ modelValue?.address.streetName }}
              </q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.address.city') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.city }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.address.postalCode') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.postalCode }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.address.stateProvince') }}</q-item-section>
              <q-item-section avatar>{{ modelValue?.address.stateProvince }}</q-item-section>
            </q-item>
            <q-item>
              <q-item-section>{{ $t('afaktoApp.address.country') }}</q-item-section>
              <q-item-section avatar>
                <template v-if="modelValue?.address?.country">
                  <em :class="`fi fi-${modelValue.address.country.toLowerCase()}`" />
                  <q-tooltip>{{ countryNames.of(modelValue.address.country.toUpperCase()) }}</q-tooltip>
                </template>
              </q-item-section>
            </q-item>


          </div>
        </q-expansion-item>
      </q-list>

      <q-separator />
      <!--      <q-list>-->
      <!--        <q-expansion-item v-if="modelValue" dense-toggle expand-separator label="History & Comments">-->
      <!--          <div class="q-pa-sm">-->
      <!--            <div>{{ modelValue?.history }}</div>-->
      <!--            <div>{{ modelValue?.comments }}</div>-->
      <!--          </div>-->
      <!--        </q-expansion-item>-->

      <!--      </q-list>-->

      <!--      <entity-meta :entity="modelValue" />-->
    </q-card>
    <footer>
      <q-btn @click="() => router.push(`/companies/${modelValue.id}`)" icon="edit" class="buttonBrand" label="Edit">

      </q-btn>
    </footer>
  </q-drawer>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

const router = useRouter();

const props = defineProps({
  modelValue: Object
});

const { locale } = useI18n();

const countryNames = new Intl.DisplayNames([navigator.language], { type: 'region' });

const monthOptions = computed(() =>
  [...Array(12).keys()].map(i => ({
    label: new Date(0, i).toLocaleString(locale.value, { month: 'long' }),
    value: i + 1
  }))
);

const getFormattedMonth = (monthNumber) => {
  if (!monthNumber) return '';
  const monthOption = monthOptions.value.find(option => option.value === monthNumber);
  return monthOption ? monthOption.label : monthNumber;
};


</script>
