<template>
  <b-input v-model="modelValue.streetNumber" :label="$t('afaktoApp.address.streetNumber')" :readonly="readonly" />
  <b-input v-model="modelValue.streetName" :label="$t('afaktoApp.address.streetName')" :readonly="readonly" />
  <b-input v-model="modelValue.postalCode" :label="$t('afaktoApp.address.postalCode')" :readonly="readonly" />
  <b-input v-model="modelValue.city" :label="$t('afaktoApp.address.city')" :readonly="readonly" />
  <b-input v-model="modelValue.stateProvince" :label="$t('afaktoApp.address.stateProvince')" :readonly="readonly" />

  <q-select
    v-model="modelValue.country"
    :label="$t('afaktoApp.contract.country')"
    :options="countries.filter(c => c.iso)"
    :readonly="readonly"
    emit-value
    filled
    map-options
    option-label="name"
    option-value="code"
  >
    <template v-if="modelValue.country" #prepend>
      <span :class="`fi fi-${modelValue.country}`"></span>
    </template>
    <template #option="scope">
      <q-item dense v-bind="scope.itemProps">
        <q-item-section side>
          <span :class="`fi fi-${scope.opt.code}`" />
        </q-item-section>
        <q-item-section>
          <q-item-label>{{ scope.opt.name }}</q-item-label>
        </q-item-section>
      </q-item>
    </template>
  </q-select>
</template>

<script setup>
import countries from 'flag-icons/country.json';

const modelValue = defineModel({ type: Object });

defineProps({
  readonly: Boolean
});

</script>
